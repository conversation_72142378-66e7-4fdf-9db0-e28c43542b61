<?php
namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    public function googleLogin($token)
    {
        // Get user from Google using token
        /** @var \Laravel\Socialite\Two\GoogleProvider $driver */
        $driver = Socialite::driver('google');
        $googleUser = $driver->stateless()->userFromToken($token);

        // Check if user exists
        $user = User::where('email', $googleUser->getEmail())->first();

        if (!$user) {
            // Create new user if doesn’t exist
            $user = User::create([
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => bcrypt(str()->random(16)), // random password
            ]);
        }

        // Issue Passport token
        $tokenResult = $user->createToken('MobileAppToken')->accessToken;

        return response()->json([
            'access_token' => $tokenResult,
            'user' => $user,
        ]);
    }
}
